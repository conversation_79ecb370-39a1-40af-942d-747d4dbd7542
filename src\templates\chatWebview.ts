import * as vscode from 'vscode';

/**
 * 聊天 Webview HTML 模板生成器
 */
export class ChatWebviewTemplate {
    
    /**
     * 生成聊天界面的 HTML (Vue应用版本)
     * @param webview Webview 实例
     * @returns HTML 字符串
     */
    static getHtml(webview: vscode.Webview, extensionUri: vscode.Uri): string {
        // 获取Vue应用的脚本URI
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(
            extensionUri, 'dist', 'webview.js'
        ));

        // 获取CSS文件URI
        const cssUri = webview.asWebviewUri(vscode.Uri.joinPath(
            extensionUri, 'dist', 'webview.css'
        ));

        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src ${webview.cspSource} 'unsafe-eval'; font-src ${webview.cspSource};">
                <title>AI 助手聊天</title>
                <style>
                    /* 基础样式，确保Vue应用正确挂载 */
                    * {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                    }

                    html, body {
                        margin: 0;
                        padding: 0;
                        height: 100%;
                        overflow: hidden;
                        font-family: var(--vscode-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
                        font-size: var(--vscode-font-size, 13px);
                        background-color: var(--vscode-editor-background, #1e1e1e);
                        color: var(--vscode-editor-foreground, #cccccc);
                        line-height: 1.4;
                    }

                    #app {
                        height: 100vh;
                        width: 100vw;
                    }

                    /* 滚动条样式 */
                    ::-webkit-scrollbar {
                        width: 8px;
                        height: 8px;
                    }

                    ::-webkit-scrollbar-track {
                        background: var(--vscode-scrollbarSlider-background, #79797966);
                    }

                    ::-webkit-scrollbar-thumb {
                        background: var(--vscode-scrollbarSlider-background, #79797966);
                        border-radius: 4px;
                    }

                    ::-webkit-scrollbar-thumb:hover {
                        background: var(--vscode-scrollbarSlider-hoverBackground, #646464b3);
                    }

                    ::-webkit-scrollbar-thumb:active {
                        background: var(--vscode-scrollbarSlider-activeBackground, #bfbfbf66);
                    }

                    /* 选中文本样式 */
                    ::selection {
                        background: var(--vscode-editor-selectionBackground, #264f78);
                        color: var(--vscode-editor-selectionForeground, #cccccc);
                    }

                    /* 焦点样式 */
                    :focus {
                        outline: 1px solid var(--vscode-focusBorder, #007acc);
                        outline-offset: -1px;
                    }

                    :focus:not(:focus-visible) {
                        outline: none;
                    }

                    /* 确保VSCode主题变量可用 */
                    :root {
                        --vscode-font-family: var(--vscode-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
                        --vscode-font-size: var(--vscode-font-size, 13px);
                        --vscode-editor-background: var(--vscode-editor-background, #1e1e1e);
                        --vscode-editor-foreground: var(--vscode-editor-foreground, #cccccc);
                        --vscode-input-background: var(--vscode-input-background, #3c3c3c);
                        --vscode-input-border: var(--vscode-input-border, #3c3c3c);
                        --vscode-input-foreground: var(--vscode-input-foreground, #cccccc);
                        --vscode-button-background: var(--vscode-button-background, #0e639c);
                        --vscode-button-foreground: var(--vscode-button-foreground, #ffffff);
                        --vscode-button-hoverBackground: var(--vscode-button-hoverBackground, #1177bb);
                        --vscode-textLink-foreground: var(--vscode-textLink-foreground, #3794ff);
                        --vscode-descriptionForeground: var(--vscode-descriptionForeground, #cccccc99);
                        --vscode-panel-border: var(--vscode-panel-border, #3c3c3c);
                        --vscode-panel-background: var(--vscode-panel-background, #252526);
                        --vscode-focusBorder: var(--vscode-focusBorder, #007acc);
                        --vscode-errorForeground: var(--vscode-errorForeground, #f48771);
                        --vscode-scrollbarSlider-background: var(--vscode-scrollbarSlider-background, #79797966);
                        --vscode-scrollbarSlider-hoverBackground: var(--vscode-scrollbarSlider-hoverBackground, #646464b3);
                        --vscode-scrollbarSlider-activeBackground: var(--vscode-scrollbarSlider-activeBackground, #bfbfbf66);
                        --vscode-textPreformat-background: var(--vscode-textPreformat-background, #0a0a0a66);
                        --vscode-textBlockQuote-background: var(--vscode-textBlockQuote-background, #7f7f7f1a);
                        --vscode-textBlockQuote-border: var(--vscode-textBlockQuote-border, #007acc80);
                    }

                    /* Vue应用加载前的占位样式 */
                    .loading-placeholder {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100vh;
                        flex-direction: column;
                        gap: 16px;
                        color: var(--vscode-descriptionForeground);
                    }

                    .loading-spinner {
                        width: 24px;
                        height: 24px;
                        border: 2px solid var(--vscode-progressBar-background);
                        border-top: 2px solid var(--vscode-textLink-foreground);
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }

                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }

                    /* Vue应用加载后隐藏占位符 */
                    #app:not(:empty) + .loading-placeholder {
                        display: none;
                    }
                </style>
            </head>
            <body>
                <!-- Vue应用挂载点 -->
                <div id="app"></div>

                <!-- 加载占位符 -->
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <div>正在加载 AI 助手...</div>
                </div>

                <!-- Vue应用脚本 -->
                <script src="${scriptUri}"></script>

            </body>
            </html>
        `;
    }
}
