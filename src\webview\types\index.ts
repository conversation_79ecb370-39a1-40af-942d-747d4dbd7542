// Vue webview types
export interface Message {
  id: string;
  type: 'user' | 'assistant' | 'error';
  content: string;
  timestamp: number;
}

export interface WebviewMessage {
  type: string;
  data?: any;
}

export interface ChatState {
  messages: Message[];
  isGenerating: boolean;
  inputValue: string;
}

// VSCode API types for webview
declare global {
  interface Window {
    acquireVsCodeApi(): {
      postMessage(message: any): void;
      setState(state: any): void;
      getState(): any;
    };
  }
}
