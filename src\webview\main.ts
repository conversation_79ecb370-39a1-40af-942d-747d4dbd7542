// Vue webview application entry point
import { createApp } from 'vue';
import { messageService } from './utils/vscode';
import ChatContainer from './components/ChatContainer.vue';

// Import global styles
import './styles/global.css';

// Import vscode-elements components
import '@vscode-elements/elements/dist/bundled.js';

// Create and mount the Vue application
const app = createApp(ChatContainer);
app.mount('#app');
