// VSCode API utilities for webview
let vscode: ReturnType<typeof window.acquireVsCodeApi> | null = null;

export function getVSCodeAPI() {
  if (!vscode) {
    vscode = window.acquireVsCodeApi();
  }
  return vscode;
}

export function postMessage(message: any) {
  const api = getVSCodeAPI();
  api.postMessage(message);
}

export function setState(state: any) {
  const api = getVSCodeAPI();
  api.setState(state);
}

export function getState() {
  const api = getVSCodeAPI();
  return api.getState();
}

// 消息通信服务类
export class MessageService {
  private listeners: Map<string, Set<Function>> = new Map();

  constructor() {
    // 监听来自扩展的消息
    window.addEventListener('message', this.handleMessage.bind(this));
  }

  // 处理来自扩展的消息
  private handleMessage(event: MessageEvent) {
    const message = event.data;
    if (message && message.type) {
      this.emit(message.type, message.data);
    }
  }

  // 发送消息到扩展
  send(type: string, data?: any) {
    postMessage({ type, data });
  }

  // 监听特定类型的消息
  on(type: string, callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(callback);
  }

  // 移除消息监听器
  off(type: string, callback: Function) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  // 触发消息事件
  private emit(type: string, data?: any) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // 清理所有监听器
  destroy() {
    this.listeners.clear();
  }
}

// 创建全局消息服务实例
export const messageService = new MessageService();
