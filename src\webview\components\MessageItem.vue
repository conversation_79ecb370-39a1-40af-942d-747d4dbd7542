<template>
  <div :class="['message', message.type]">
    <!-- 消息头像 -->
    <div class="message-avatar">
      <span v-if="message.type === 'user'">👤</span>
      <span v-else-if="message.type === 'assistant'">🤖</span>
      <span v-else-if="message.type === 'error'">❌</span>
    </div>
    
    <!-- 消息内容 -->
    <div class="message-content">
      <div class="message-text" v-html="formattedContent"></div>
      <div class="message-timestamp">
        {{ formatTimestamp(message.timestamp) }}
      </div>
    </div>
    
    <!-- 消息操作按钮 -->
    <div class="message-actions" v-if="message.type !== 'error'">
      <vscode-button 
        appearance="icon" 
        @click="copyMessage"
        title="复制消息"
      >
        <vscode-icon name="copy"></vscode-icon>
      </vscode-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { Message } from '../types';

// 导入vscode-elements组件
import '@vscode-elements/elements/dist/vscode-button';
import '@vscode-elements/elements/dist/vscode-icon';

interface Props {
  message: Message;
}

const props = defineProps<Props>();

// 格式化消息内容（支持简单的markdown）
const formattedContent = computed(() => {
  let content = props.message.content;
  
  // 转义HTML
  content = content.replace(/&/g, '&amp;')
                  .replace(/</g, '&lt;')
                  .replace(/>/g, '&gt;');
  
  // 简单的markdown支持
  content = content
    // 代码块
    .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
    // 行内代码
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    // 粗体
    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
    // 斜体
    .replace(/\*([^*]+)\*/g, '<em>$1</em>')
    // 换行
    .replace(/\n/g, '<br>');
  
  return content;
});

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  // 如果是今天
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }
  
  // 如果是昨天或更早
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 复制消息内容
const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content);
    // 可以添加一个toast提示
  } catch (err) {
    console.error('复制失败:', err);
  }
};
</script>

<style scoped>
.message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  position: relative;
}

.message:hover .message-actions {
  opacity: 1;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: var(--vscode-textLink-foreground);
  color: white;
}

.message.assistant .message-avatar {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.message.error .message-avatar {
  background: var(--vscode-errorForeground);
  color: white;
}

.message-content {
  flex: 1;
  background: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 12px;
  padding: 12px 16px;
  max-width: 80%;
  position: relative;
}

.message.error .message-content {
  background: var(--vscode-inputValidation-errorBackground);
  border-color: var(--vscode-inputValidation-errorBorder);
}

.message-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.message-text :deep(pre) {
  background: var(--vscode-textBlockQuote-background);
  border: 1px solid var(--vscode-textBlockQuote-border);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  overflow-x: auto;
}

.message-text :deep(code) {
  background: var(--vscode-textPreformat-background);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--vscode-editor-font-family);
  font-size: 0.9em;
}

.message-text :deep(pre code) {
  background: none;
  padding: 0;
}

.message-timestamp {
  font-size: 0.75em;
  color: var(--vscode-descriptionForeground);
  margin-top: 4px;
}

.message-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.message-actions vscode-button {
  --vscode-button-paddingHorizontal: 6px;
  --vscode-button-paddingVertical: 6px;
}
</style>
